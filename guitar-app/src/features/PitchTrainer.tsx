"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { But<PERSON> } from "../shared/ui/button"
import { Label } from "../shared/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../shared/ui/dialog"
import { Setting<PERSON>, Refresh<PERSON>w, Shuffle } from 'lucide-react'
import { Slider } from "../shared/ui/slider"
import { Select } from "../shared/ui/select"
import { Switch } from "../shared/ui/switch"
import { Scale, scalePatterns } from '../constants/musicTheory'
import BackButton from "../shared/ui/back-button"

type NoteShape = 'rectangle' | 'triangle-up' | 'triangle-down'
type DifficultyLevel = '1 нота' | '2 ноты' | '3 ноты'
type GameMode = 'standard' | 'repeat' | 'sing'

interface Note {
  note: string
  color: string
  shape: NoteShape
  midiOffset: number
  isDisabled?: boolean
}

interface GameState {
  key: string
  scale: Scale
  isRandomKey: boolean
  isRandomScale: boolean
  autoPlay: boolean
  showNote: boolean
  isPlaying: boolean
  correct: number
  incorrect: number
  speed: number
  isBassEnabled: boolean
  repeatOnError: boolean
  muteBtnSound: boolean
  difficultyLevel: DifficultyLevel
  gameMode: GameMode
  tempo: number
}

const initialState: GameState = {
  key: 'C',
  scale: 'Natural Major',
  isRandomKey: false,
  isRandomScale: false,
  autoPlay: false,
  showNote: false,
  isPlaying: false,
  correct: 0,
  incorrect: 0,
  speed: 0,
  isBassEnabled: true,
  repeatOnError: false,
  muteBtnSound: true,
  difficultyLevel: '1 нота',
  gameMode: 'standard',
  tempo: 60,
}

const allNotes: Note[] = [
  { note: '5', color: 'bg-[#9ACD32]', shape: 'rectangle', midiOffset: 7 },
  { note: '#4', color: 'bg-[#FF2400]', shape: 'triangle-up', midiOffset: 6 },
  { note: '4', color: 'bg-[#7FFFD4]', shape: 'triangle-down', midiOffset: 5 },
  { note: '3', color: 'bg-[#FFA500]', shape: 'triangle-up', midiOffset: 4 },
  { note: 'b3', color: 'bg-[#0000FF]', shape: 'triangle-down', midiOffset: 3 },
  { note: '2', color: 'bg-[#FAFA33]', shape: 'triangle-up', midiOffset: 2 },
  { note: 'b2', color: 'bg-[#8A2BE2]', shape: 'triangle-down', midiOffset: 1 },
  { note: '1', color: 'bg-[#008000]', shape: 'rectangle', midiOffset: 0 },
  { note: '7', color: 'bg-[#FF0000]', shape: 'triangle-up', midiOffset: -1 },
  { note: 'b7', color: 'bg-[#87CEEB]', shape: 'triangle-down', midiOffset: -2 },
  { note: '6', color: 'bg-[#FFFF00]', shape: 'triangle-up', midiOffset: -3 },
  { note: 'b6', color: 'bg-[#8A2BE2]', shape: 'triangle-down', midiOffset: -4 },
  { note: '5', color: 'bg-[#9ACD32]', shape: 'rectangle', midiOffset: -5 },
]

const noteMidiNumbers: { [key: string]: number } = {
  'C': 60, 'C#/Db': 61, 'D': 62, 'D#/Eb': 63, 'E': 64, 'F': 65,
  'F#/Gb': 66, 'G': 67, 'G#/Ab': 68, 'A': 69, 'A#/Bb': 70, 'B': 71
}

// scalePatterns импортируется из '../data/scales'

const NoteButton = React.memo<{
  note: Note | undefined
  onClick: () => void
  isCorrect: boolean | null
  isActive: boolean
  isDisabled: boolean
  showNote: boolean
  isPlayback: boolean
  animationDuration: number
}>(({ note, onClick, isCorrect, isActive, isDisabled, showNote, animationDuration }) => (
  <div
    data-duration={animationDuration}
    className={`w-1/2 mx-auto h-full flex items-center justify-center font-bold text-sm sm:text-base cursor-pointer transition-all duration-300 shadow-md rounded-md
      ${isDisabled ? 'bg-gray-200' : note?.color || 'bg-gray-300'}
      ${note?.shape === 'triangle-up' ? 'clip-path-triangle-up' : note?.shape === 'triangle-down' ? 'clip-path-triangle-down' : ''}
      ${isCorrect === true ? 'animate-flash-green' : isCorrect === false ? 'animate-flash-red' : ''}
      ${isActive && showNote ? 'animate-flash-white' : ''}
      ${!isDisabled ? 'hover:brightness-125 hover:scale-110 active:scale-95' : ''}`}
    onClick={isDisabled ? undefined : onClick}
  >
    {!isDisabled && note?.note}
  </div>
))

const useAudio = () => {
  const audioContextRef = useRef<AudioContext | null>(null)
  const bassOscillatorRef = useRef<OscillatorNode | null>(null)
  const bassGainRef = useRef<GainNode | null>(null)

  const getAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }
    return audioContextRef.current
  }, [])

  const playNote = useCallback((midiNote: number, isBass = false) => {
    const context = getAudioContext()
    const osc = context.createOscillator()
    const gain = context.createGain()
    osc.type = 'sine'
    osc.frequency.setValueAtTime(440 * Math.pow(2, (midiNote - 69) / 12), context.currentTime)

    if (isBass) {
      if (bassOscillatorRef.current) {
        bassOscillatorRef.current.stop()
        bassGainRef.current?.disconnect()
      }
      bassOscillatorRef.current = osc
      bassGainRef.current = gain
      gain.gain.setValueAtTime(0, context.currentTime)
      gain.gain.linearRampToValueAtTime(0.2, context.currentTime + 2)
      osc.start()
    } else {
      gain.gain.setValueAtTime(0.7, context.currentTime)
      gain.gain.exponentialRampToValueAtTime(0.001, context.currentTime + 1.5)
      osc.start()
      osc.stop(context.currentTime + 1.5)
    }

    osc.connect(gain).connect(context.destination)
  }, [getAudioContext])

  const startBass = useCallback((key: string) => {
    playNote(noteMidiNumbers[key] - 12, true)
  }, [playNote])

  const stopBass = useCallback(() => {
    if (bassGainRef.current) {
      bassGainRef.current.gain.linearRampToValueAtTime(0, getAudioContext().currentTime + 2)
    }
  }, [getAudioContext])

  const updateBassFreq = useCallback((key: string) => {
    if (bassOscillatorRef.current) {
      bassOscillatorRef.current.frequency.setValueAtTime(
        440 * Math.pow(2, (noteMidiNumbers[key] - 69 - 12) / 12),
        getAudioContext().currentTime
      )
    }
  }, [getAudioContext])

  const cleanup = useCallback(() => {
    if (audioContextRef.current) {
      if (bassOscillatorRef.current) {
        bassOscillatorRef.current.stop();
        bassOscillatorRef.current = null;
      }
      if (bassGainRef.current) {
        bassGainRef.current.disconnect();
        bassGainRef.current = null;
      }
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  }, []);

  return { playNote, startBass, stopBass, updateBassFreq, cleanup }
}

const useGameLogic = (state: GameState, setState: React.Dispatch<React.SetStateAction<GameState>>, audio: ReturnType<typeof useAudio>) => {
  const [notes, setNotes] = useState<Note[]>(allNotes)
  const [targetSequence, setTargetSequence] = useState<Note[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [noteStatus, setNoteStatus] = useState<{[key: string]: boolean | null}>({})
  const [isPlayback, setIsPlayback] = useState(false)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [gameStartTime, setGameStartTime] = useState<number | null>(null)
  const [animationDuration, setAnimationDuration] = useState(0.5)

  const timeoutRefs = useRef<NodeJS.Timeout[]>([])
  const startTimeRef = useRef<number | null>(null)

  const generateRandomNote = useCallback(() => {
    const activeNotes = notes.filter(note => !note.isDisabled)
    return activeNotes[Math.floor(Math.random() * activeNotes.length)]
  }, [notes])

  const generateTargetSequence = useCallback(() => {
    const sequenceLength = state.difficultyLevel === '1 нота' ? 1 : state.difficultyLevel === '2 ноты' ? 2 : 3
    const newSequence: Note[] = Array.from({ length: sequenceLength }, () => generateRandomNote())
    setTargetSequence(newSequence)
    setCurrentIndex(0)
    setNoteStatus({})
    return newSequence
  }, [state.difficultyLevel, generateRandomNote])

  const playSequence = useCallback((sequence: Note[], withSound: boolean) => {
    setIsPlayback(true)
    const beatDuration = (60 / state.tempo) * 1000
    const TIME_SIGNATURE = 4
    const barDuration = beatDuration * TIME_SIGNATURE

    if (!startTimeRef.current) {
      startTimeRef.current = Date.now()
    }

    const playNote = (note: Note, index: number) => {
      const timeout1 = setTimeout(() => {
        if (state.showNote || state.gameMode === 'sing') {
          setNoteStatus({})
          setTimeout(() => {
            setNoteStatus(prev => ({ ...prev, [note.note + note.midiOffset]: true }))
          }, 0)
        }
        if (withSound) {
          audio.playNote(noteMidiNumbers[state.key] + note.midiOffset)
        }
      }, index * beatDuration)

      const timeout2 = setTimeout(() => {
        if (state.showNote || state.gameMode === 'sing') {
          setNoteStatus(prev => ({ ...prev, [note.note + note.midiOffset]: null }))
        }
      }, (index + 1) * beatDuration)

      timeoutRefs.current.push(timeout1, timeout2)
    }

    sequence.forEach((note, index) => {
      playNote(note, index)
    })

    const pauseDuration = barDuration - (sequence.length * beatDuration)
    if (pauseDuration > 0) {
      const pauseTimeout = setTimeout(() => {
        // Пауза после последовательности
      }, sequence.length * beatDuration + pauseDuration)

      timeoutRefs.current.push(pauseTimeout)
    }

    const nextActionTimeout = setTimeout(() => {
      setIsPlayback(false)
      if (state.gameMode === 'repeat') {
        setTimeout(() => {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, true)
        }, barDuration)
      } else if (state.gameMode === 'sing') {
        if (!withSound) {
          setTimeout(() => {
            playSequence(sequence, true)
          }, barDuration)
        } else {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, false)
        }
      } else if (state.autoPlay) {
        const newSequence = generateTargetSequence()
        playSequence(newSequence, true)
      }
    }, barDuration)
    timeoutRefs.current.push(nextActionTimeout)
    if (state.gameMode === 'repeat' || (state.gameMode === 'sing' && !withSound)) {
      const emptyMeasureTimeout = setTimeout(() => {}, barDuration)
      timeoutRefs.current.push(emptyMeasureTimeout)
    }

  }, [state, audio, generateTargetSequence])

  const replaySequence = useCallback(() => {
    if (targetSequence.length === 0) return
    playSequence(targetSequence, true)
  }, [targetSequence, playSequence])

  const handleStart = useCallback(() => {
    if (state.isPlaying) {
      setState(prev => ({ ...prev, isPlaying: false }))
      setTargetSequence([])
      setCurrentIndex(0)
      setNoteStatus({})
      audio.stopBass()
      timeoutRefs.current.forEach(clearTimeout)
      timeoutRefs.current = []
    } else {
      const newKey = state.isRandomKey ? Object.keys(noteMidiNumbers)[Math.floor(Math.random() * 12)] : state.key
      const newScale = state.isRandomScale ? Object.keys(scalePatterns)[Math.floor(Math.random() * Object.keys(scalePatterns).length)] as Scale : state.scale

      setState(prev => ({
        ...prev,
        isPlaying: true,
        correct: 0,
        incorrect: 0,
        key: newKey,
        scale: newScale,
        showNote: prev.gameMode === 'sing' ? true : prev.showNote,
        tempo: prev.tempo,
        speed: 0
      }))
      setGameStartTime(Date.now())
      setElapsedTime(0)

      if (state.isBassEnabled) {
        audio.startBass(newKey)
        audio.updateBassFreq(newKey)
      }

      const zeroMeasureDelay = (60 / state.tempo) * 1000 * 4
      setTimeout(() => {
        const newSequence = generateTargetSequence()
        playSequence(newSequence, state.gameMode !== 'sing')
      }, zeroMeasureDelay)
    }
  }, [state, generateTargetSequence, playSequence, audio, setState])

  const handleNoteClick = useCallback((clickedNote: Note | undefined) => {
    if (!clickedNote || state.gameMode === 'sing' || state.autoPlay || !state.isPlaying || targetSequence.length === 0) return
    if (!state.muteBtnSound) {
      audio.playNote(noteMidiNumbers[state.key] + clickedNote.midiOffset)
    }
    const beatDuration = (60 / state.tempo) * 1000

    const isCorrect = clickedNote.note === targetSequence[currentIndex].note && clickedNote.midiOffset === targetSequence[currentIndex].midiOffset
    setNoteStatus(prev => ({ ...prev, [clickedNote.note + clickedNote.midiOffset]: isCorrect }))
    if (isCorrect) {
      setCurrentIndex(prev => prev + 1)
      setState(prev => ({ ...prev, correct: prev.correct + 1 }))
      if (currentIndex === targetSequence.length - 1) {
        setTimeout(() => {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, true)
        }, beatDuration)
      }
    } else {
      setState(prev => ({ ...prev, incorrect: prev.incorrect + 1 }))
      if (state.repeatOnError) {
        setTimeout(replaySequence, beatDuration)
      }
    }
    setTimeout(() => setNoteStatus(prev => ({ ...prev, [clickedNote.note + clickedNote.midiOffset]: null })), beatDuration / 2)
  }, [state, currentIndex, targetSequence, generateTargetSequence, playSequence, audio, setState, replaySequence])

  useEffect(() => {
    const updateActiveNotes = () => {
      const activeNoteNames = scalePatterns[state.scale]
      setNotes(allNotes.map(note => ({
        ...note,
        isDisabled: state.scale === 'Chromatic' ? false : !activeNoteNames.includes(note.note)
      })))
    }
    updateActiveNotes()
  }, [state.scale])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (state.isPlaying) {
      interval = setInterval(() => {
        const currentTime = Date.now()
        const elapsedSeconds = gameStartTime ? Math.floor((currentTime - gameStartTime) / 1000) : 0
        setElapsedTime(elapsedSeconds)
        if (gameStartTime && state.correct > 0) {
          const elapsedMinutes = (currentTime - gameStartTime) / 60000
          const newSpeed = Math.round(state.correct / elapsedMinutes)
          const newTempo = Math.max(30, Math.min(240, newSpeed * 4))
          setState(prev => ({
            ...prev,
            speed: newSpeed,
            tempo: elapsedSeconds > 5 ? newTempo : prev.tempo
          }))
          setAnimationDuration(60 / newTempo)
        }
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [state.isPlaying, state.correct, gameStartTime, setState])

  return {
    notes,
    targetSequence,
    currentIndex,
    noteStatus,
    isPlayback,
    elapsedTime,
    animationDuration,
    handleStart,
    handleNoteClick,
    generateTargetSequence,
    replaySequence
  }
}

const SettingsSection: React.FC<{ title: string; children: React.ReactNode; disabled?: boolean }> = ({ title, children, disabled }) => (
  <div className={disabled ? 'opacity-50 pointer-events-none' : ''}>
    <Label className="text-lg font-bold mb-2">{title}</Label>
    <div className="space-y-2 ml-4">{children}</div>
  </div>
)

const SwitchSetting: React.FC<{ id: string; label: string; checked: boolean; onChange: (checked: boolean) => void; disabled?: boolean }> = ({ id, label, checked, onChange, disabled }) => (
  <div className="flex items-center justify-between cursor-pointer">
    <Label htmlFor={id} className={`text-sm ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>{label}</Label>
    <Switch
      id={id}
      checked={checked}
      onCheckedChange={onChange}
      disabled={disabled}
    />
  </div>
)

export default function Component() {
  const [state, setState] = useState<GameState>(initialState)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const audio = useAudio()
  const game = useGameLogic(state, setState, audio)

  useEffect(() => {
    return () => {
      if (state.isPlaying) {
        setState(prev => ({ ...prev, isPlaying: false }));
        audio.stopBass();
      }
      audio.cleanup();
    };
  }, [audio, state.isPlaying, setState]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const calculatePercentage = (value: number, total: number) => {
    return total === 0 ? 0 : Math.round((value / total) * 100)
  }

  const renderSettingsDialog = () => (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="max-w-md w-full h-[90vh] overflow-y-auto p-4 sm:p-6 sm:max-w-[90vw]">
        <DialogHeader>
          <DialogTitle>Настройки</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-2 overflow-y-auto">
          <SettingsSection title="Режим игры">
            {['standard', 'repeat', 'sing'].map((mode) => (
              <SwitchSetting
                key={mode}
                id={`${mode}Mode`}
                label={mode === 'standard' ? 'Стандартный' : mode === 'repeat' ? 'Повторяем на инструменте' : 'Поем голосом'}
                checked={state.gameMode === mode}
                onChange={(checked: boolean) => {
                  if (checked) {
                    setState(prev => ({
                      ...prev,
                      gameMode: mode as GameMode,
                      autoPlay: mode === 'repeat',
                      showNote: mode === 'sing' ? true : prev.showNote
                    }))
                  }
                }}
              />
            ))}
          </SettingsSection>

          <SettingsSection title="" disabled={state.gameMode === 'standard'}>
            <div className="flex items-center space-x-2">
              <span className="text-sm whitespace-nowrap">Темп:</span>
              <div className="flex-grow">
                <Slider
                  id="bpm"
                  min={30}
                  max={240}
                  step={1}
                  value={state.tempo.toString()}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState(prev => ({ ...prev, tempo: parseInt(e.target.value) }))}
                />
              </div>
              <span className="text-sm whitespace-nowrap">{state.tempo} BPM</span>
            </div>
          </SettingsSection>

          <SettingsSection title="">
            <div className="flex items-center space-x-2">
              <span className="text-sm whitespace-nowrap">Сложность:</span>
              <div className="flex-grow">
                <Slider
                  id="difficulty"
                  min={1}
                  max={3}
                  step={1}
                  value={state.difficultyLevel === '1 нота' ? '1' : state.difficultyLevel === '2 ноты' ? '2' : '3'}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState(prev => ({
                    ...prev,
                    difficultyLevel: e.target.value === '1' ? '1 нота' : e.target.value === '2' ? '2 ноты' : '3 ноты'
                  }))}
                />
              </div>
              <span className="text-sm whitespace-nowrap">{state.difficultyLevel}</span>
            </div>
          </SettingsSection>

          <SettingsSection title="Тональность">
            <div className="grid grid-cols-12 gap-2">
              <div className="col-span-3">
                <Select
                  options={Object.keys(noteMidiNumbers).map((key) => ({ label: key, value: key }))}
                  value={state.key}
                  onValueChange={(value) => setState(prev => ({ ...prev, key: value }))}
                  disabled={state.isRandomKey}
                  placeholder="Тон."
                />
              </div>
              <div className="col-span-2 flex items-center justify-center">
                <button
                  type="button"
                  onClick={() => setState(prev => ({ ...prev, isRandomKey: !prev.isRandomKey }))}
                  className={`w-10 h-10 rounded flex items-center justify-center ${state.isRandomKey ? "bg-blue-600 text-white" : "border border-gray-300"}`}
                >
                  <Shuffle size={20} />
                </button>
              </div>
              <div className="col-span-5">
                <Select
                  options={[
                    { label: 'Chromatic', value: 'Chromatic' },
                    ...Object.keys(scalePatterns)
                      .filter(scale => scale !== 'Chromatic')
                      .map(scale => ({ label: scale, value: scale }))
                  ]}
                  value={state.scale}
                  onValueChange={(value) => setState(prev => ({ ...prev, scale: value as Scale }))}
                  disabled={state.isRandomScale}
                  placeholder="Выберите звукоряд"
                />
              </div>
              <div className="col-span-2 flex items-center justify-center">
                <button
                  type="button"
                  onClick={() => setState(prev => ({ ...prev, isRandomScale: !prev.isRandomScale }))}
                  className={`w-10 h-10 rounded flex items-center justify-center ${state.isRandomScale ? "bg-blue-600 text-white" : "border border-gray-300"}`}
                >
                  <Shuffle size={20} />
                </button>
              </div>
            </div>
          </SettingsSection>

          <SettingsSection title="Звук">
            <div className="flex flex-col space-y-2">
              <SwitchSetting
                id="enableBass"
                label="Включить бас"
                checked={state.isBassEnabled}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, isBassEnabled: checked }))}
              />
              <SwitchSetting
                id="enableButtonSound"
                label="Включить звук кнопок"
                checked={!state.muteBtnSound}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, muteBtnSound: !checked }))}
              />
            </div>
          </SettingsSection>

          <SettingsSection title="Подсказка">
            <div className="flex flex-col space-y-2">
              <SwitchSetting
                id="showTargetNote"
                label="Показывать текущую ноту"
                checked={state.showNote}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, showNote: checked }))}
                disabled={state.gameMode === 'sing'}
              />
              <SwitchSetting
                id="repeatTargetOnError"
                label="Повторять ноты при ошибке"
                checked={state.repeatOnError}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, repeatOnError: checked }))}
                disabled={state.gameMode === 'repeat' || state.gameMode === 'sing'}
              />
            </div>
          </SettingsSection>
        </div>
        <Button onClick={() => setIsDialogOpen(false)} className="w-full mt-4 py-2 text-sm">
          Применить
        </Button>
      </DialogContent>
    </Dialog>
  )

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-2 relative">
      {/* Кнопка "Назад" - перемещена правее, чтобы не перекрывать гамбургер-меню */}
      <BackButton className="absolute top-4 left-16" />

      <style>{`
        .clip-path-triangle-up { clip-path: polygon(50% 0%, 0% 100%, 100% 100%); border-radius: 0.375rem; }
        .clip-path-triangle-down { clip-path: polygon(0% 0%, 100% 0%, 50%   100%); border-radius: 0.375rem; }
        @keyframes flash-red { 0%, 100% { background-color: inherit; } 50% { background-color: #F44336; } }
        .animate-flash-red { animation: flash-red 0.5s; animation-duration: attr(data-duration s, 0.5s); }
        @keyframes flash-green { 0%, 100% { background-color: inherit; } 50% { background-color: #4CAF50; } }
        .animate-flash-green { animation: flash-green 0.5s; animation-duration: attr(data-duration s, 0.5s); }
        @keyframes flash-white { 0%, 100% { background-color: inherit; } 50% { background-color: #FFFFFF; } }
        .animate-flash-white { animation: flash-white 0.5s; animation-duration: attr(data-duration s, 0.5s); }
      `}</style>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md w-full max-w-md aspect-[9/16] flex flex-col items-center justify-between relative p-4 text-gray-900 dark:text-gray-100">
        <div className="w-full flex justify-between items-center mb-4">
          <Button
            className="icon-button active:scale-90 hover:brightness-90 transition-transform duration-100 w-12 h-12"
            onClick={game.replaySequence}
            disabled={!state.isPlaying || state.gameMode === 'repeat'}
          >
            <RefreshCw size={20} />
          </Button>
          <Button
            onClick={game.handleStart}
            className={`flex-grow mx-2 font-bold py-1 px-4 rounded-md text-sm shadow-lg transition-all duration-300 active:scale-90 hover:brightness-90 h-12 ${
              state.isPlaying
                ? 'bg-red-500 hover:bg-red-600 text-white active:bg-red-700 active:shadow-inner'
                : 'bg-green-500 hover:bg-green-600 text-white active:bg-green-700 active:shadow-inner'
            }`}
          >
            {state.isPlaying ? 'Стоп' : 'Старт'}
          </Button>
          <Button
              className="icon-button active:scale-90 hover:brightness-90 transition-transform duration-100 w-12 h-12"
              onClick={() => setIsDialogOpen(true)}
            >
              <Settings size={20} />
            </Button>
        </div>
        <div className="mb-4 text-center w-full">
          <p className="text-sm sm:text-base font-bold">Время: {formatTime(game.elapsedTime)}</p>
          <p className="text-sm sm:text-base font-bold">
            Счет: <span className="text-green-500">{state.correct}</span>:<span className="text-red-500">{state.incorrect}</span>
            <span className="ml-1">
              <span className="text-green-500">{calculatePercentage(state.correct, state.correct + state.incorrect)}%</span>:
              <span className="text-red-500">{calculatePercentage(state.incorrect, state.correct + state.incorrect)}%</span>
            </span>
          </p>
          <p className="text-sm sm:text-base font-bold">
            Скорость: {state.speed} нот/мин : {state.speed * 4} BPM
          </p>
        </div>
        <div className="flex-grow w-full flex flex-col gap-1 sm:gap-2">
          {game.notes.map((note, index) => (
            <NoteButton
              key={`${index}-${game.noteStatus[note?.note + (note?.midiOffset ?? 0)] || ''}`}
              note={note}
              onClick={() => note && game.handleNoteClick(note)}
              isCorrect={note ? game.noteStatus[note.note + note.midiOffset] : null}
              isActive={note && game.targetSequence[game.currentIndex]?.note === note.note && game.targetSequence[game.currentIndex]?.midiOffset === note.midiOffset}
              isDisabled={note?.isDisabled ?? true}
              showNote={state.showNote}
              isPlayback={game.isPlayback}
              animationDuration={game.animationDuration}
            />
          ))}
        </div>
        {renderSettingsDialog()}
      </div>
    </div>
  )
}